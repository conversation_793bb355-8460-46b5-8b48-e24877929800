# 码上通 (CodePass) - AI Assistant Guidelines

## Project Overview
This is a **uni-app** Vue 2 hybrid mobile application for State Grid field service management, featuring form filling, task management, QR code scanning, geofencing, and map navigation.

### Key Architecture
- **Framework**: uni-app (cross-platform Vue 2)
- **UI**: Vant mobile components + Tailwind CSS
- **State**: Vuex store with modules
- **Routing**: uni-app page navigation (`uni.navigateTo`, `uni.reLaunch`)
- **Auth**: Token-based with mobile-igw-func integration

## Core Domain Patterns

### 1. Work Order Types & Navigation Flow
The app handles multiple work order types with specific routing patterns:
```javascript
// Form types determine button visibility and navigation
const formTypes = {
  "1": "日常巡视", "2": "特殊巡视", "3": "日常走访", 
  "4": "特殊走访", "5": "工单走访", "6": "工单巡视",
  "7": "默认走访", "8": "默认巡视"
};

// Navigation with geofencing parameters
uni.navigateTo({
  url: `/pages/form/index?formId=${formId}&formDataId=${formDataId}&tgNo=${towerId}`
});
```

### 2. Geofencing (电子围栏) System
Critical location validation using SGMap SDK:
```javascript
// Core geofencing check in pages/form/index.vue & pages/no-form/index.vue
async getUserLocation() {
  // 1. Get target coordinates via API using tgNo (台区编号)
  await this.getTargetLocationFromAPI();
  
  // 2. Use SGMap.GeolocationTask for user location
  const geoTask = new SGMap.GeolocationTask({
    timeout: 15000,
    enableHighAccuracy: true
  });
  
  // 3. Calculate distance and validate radius (default 1000m)
  const distance = this.calculateDistance(lat1, lng1, lat2, lng2);
  this.isInsideGeofence = distance <= this.geofenceRadius;
}
```

### 3. Logging System (`utils/logger.js`)
Structured logging with local storage persistence:
```javascript
import { writeLog, LogLevel } from "@/utils/logger";

writeLog(LogLevel.INFO, "moduleName", "operation description", {
  additionalData: "context"
});
// Levels: DEBUG, INFO, WARN, ERROR
// Stored in localStorage with 100-entry limit
```

### 4. Multi-Tab State Management
Complex tab system in `pages/index.vue`:
```javascript
// Main tabs: visit, inspect, responsibility, board
// Sub-tabs: task, manual
// Data separation between visitSubTab and inspectSubTab
watch: {
  mainTab(newVal) {
    if (newVal === "visit") this.fetchOrders();
    else if (newVal === "inspect") this.fetchInspectOrders();
  }
}
```

## Development Workflows

### Environment Detection
```javascript
// Use isProd() from @/utils/env for environment-specific behavior
import { isProd } from "@/utils/env";

if (isProd()) {
  // Production: Use SGMap microapp integration
  wx.invoke("multiWindows_startWidget", {...});
} else {
  // Development: Use local map pages
  uni.navigateTo({ url: "/pages/map/index" });
}
```

### API Request Pattern
All APIs use URL code mapping for security:
```javascript
// api/index.js pattern
import { URL_CODE_MAP } from "../static/codepass-urlCode";

const post = (url, urlCode, data, params) => {
  return request({
    url, method: "post", data: filteredData, 
    params: filteredParams, urlCode
  });
};
```

### Form Rendering
Dynamic forms use external VmFormRender component:
```javascript
// Static imports in main.js
import VmFormRender from "./static/codepass-VmFormRender.umd.min.js";
import "./static/codepass-VmFormRender.css";
```

## Critical File Locations

### Configuration
- `config.js` - Environment-based API endpoints
- `manifest.json` - uni-app build configuration
- `main.js` - App initialization with mobile-igw-func
- `utils/env.js` - Environment detection utilities

### Core Pages
- `pages/index.vue` - Main dashboard with complex tab system
- `pages/form/index.vue` - Dynamic form with geofencing
- `pages/no-form/index.vue` - Manual form entry with geofencing
- `pages/scan/index.vue` - QR code scanning entry point
- `pages/map/index.vue` - SGMap navigation integration

### Utilities
- `utils/logger.js` - Structured logging system
- `utils/request.js` - API wrapper with security headers
- `store/modules/user.js` - Authentication state management

## Integration Points

### SGMap SDK
Third-party map service for geolocation and navigation:
```javascript
// Initialization pattern
await SGMap.tokenTask.login(this.key, this.secret);
await SGMap.plugin(["SGMap.GeolocationTask"]);
```

### Mobile IGW Integration
Enterprise mobile platform integration:
```javascript
// main.js initialization
Vue.use(mobileIgwFunc, config, (userInfo) => {
  // Authentication callback
});
```

## Common Patterns

### Error Handling
```javascript
try {
  // Operation
} catch (error) {
  writeLog(LogLevel.ERROR, "module", "operation failed", { error: error.message });
  uni.showToast({ title: error.response?.msg || "操作失败", icon: "none" });
}
```

### Page Lifecycle
```javascript
onShow() {
  // Re-fetch data when returning from other pages
  if (this.hasInitialized) {
    this.fetchOrders();
  }
}
```

### Dynamic Component Loading
```javascript
components: {
  "board-component": () => import("@/pages/board/index.vue")
}
```

## Development Guidelines

1. **Always use writeLog()** for debugging and production monitoring
2. **Check environment** with `isProd()` for production vs development behavior
3. **Pass tgNo parameter** when forms require geofencing validation
4. **Handle geofencing gracefully** - allow submission even if location fails
5. **Use URL code mapping** for all API calls via URL_CODE_MAP
6. **Follow uni-app conventions** for navigation and lifecycle methods

Remember: This app serves field service workers who may have limited connectivity, so robust error handling and offline-capable patterns are essential.
